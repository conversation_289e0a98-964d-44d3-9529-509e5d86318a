<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Reloj Digital con Beep</title>
  <style>
    body {
      margin: 0;
      height: 100vh;
      background: linear-gradient(135deg, #0f2027, #203a43, #2c5364);
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: white;
    }

    .container {
      text-align: center;
      padding: 40px;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 20px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
      backdrop-filter: blur(10px);
    }

    #clock {
      font-size: 6em;
      font-weight: 600;
      margin-bottom: 20px;
      letter-spacing: 2px;
      cursor: pointer;
      user-select: none;
    }

    #date {
      font-size: 1.5em;
      opacity: 0.9;
    }

    #toggleFormat {
      font-size: 0.9em;
      margin-top: 15px;
      opacity: 0.6;
    }
  </style>
</head>
<body>

  <div class="container">
    <div id="clock">--:--:--</div>
    <div id="date">Cargando fecha...</div>
    <div id="toggleFormat">(Haz clic en la hora para cambiar el formato)</div>
  </div>

  <script>
    let is24Hour = true;
    const beepEnabled = true;

    function playBeep() {
      if (!beepEnabled) return;
      const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioCtx.createOscillator();
      const gain = audioCtx.createGain();

      oscillator.type = 'sine';
      oscillator.frequency.setValueAtTime(800, audioCtx.currentTime); // Frecuencia del beep
      gain.gain.setValueAtTime(0.1, audioCtx.currentTime); // Volumen

      oscillator.connect(gain);
      gain.connect(audioCtx.destination);
      oscillator.start();
      oscillator.stop(audioCtx.currentTime + 0.05); // Duración corta
    }

    function updateClock() {
      const now = new Date();
      let hours = now.getHours();
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      let period = '';
      if (!is24Hour) {
        period = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12 || 12;
      }
      hours = String(hours).padStart(2, '0');

      document.getElementById('clock').textContent = `${hours}:${minutes}:${seconds} ${!is24Hour ? period : ''}`.trim();

      const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
      const formattedDate = now.toLocaleDateString('es-ES', options);
      document.getElementById('date').textContent = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

      playBeep();
    }

    document.getElementById('clock').addEventListener('click', () => {
      is24Hour = !is24Hour;
      updateClock();
    });

    updateClock();
    setInterval(updateClock, 1000);
  </script>

</body>
</html>